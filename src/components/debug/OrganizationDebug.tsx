import { useOrganizationContext } from '@/contexts/OrganizationContext';
import { useOrganizations } from '@/hooks/useOrganizations';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export const OrganizationDebug = () => {
  const { currentOrganization, organizations, isLoading, error } = useOrganizationContext();
  const { organizations: hookOrgs, isLoading: hookLoading, error: hookError } = useOrganizations();

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>Organization Context Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <strong>Loading State:</strong> {isLoading ? 'Loading...' : 'Loaded'}
          </div>
          
          <div>
            <strong>Error:</strong> {error ? error.message : 'None'}
          </div>
          
          <div>
            <strong>Organizations Count:</strong> {organizations.length}
          </div>
          
          <div>
            <strong>Current Organization:</strong> {currentOrganization ? currentOrganization.name : 'None'}
          </div>
          
          {organizations.length > 0 && (
            <div>
              <strong>All Organizations:</strong>
              <div className="mt-2 space-y-2">
                {organizations.map(org => (
                  <div key={org.id} className="flex items-center gap-2">
                    <Badge variant={currentOrganization?.id === org.id ? 'default' : 'outline'}>
                      {org.name}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      Role: {org.user_role}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Hook Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <strong>Hook Loading:</strong> {hookLoading ? 'Loading...' : 'Loaded'}
          </div>
          
          <div>
            <strong>Hook Error:</strong> {hookError ? hookError.message : 'None'}
          </div>
          
          <div>
            <strong>Hook Organizations:</strong> {hookOrgs.length}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
