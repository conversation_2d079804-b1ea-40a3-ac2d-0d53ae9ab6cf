import { useState } from 'react';
import { useOrganizationContext } from '@/contexts/OrganizationContext';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useOrganizations } from '@/hooks/useOrganizations';
import { 
  Building2, 
  ChevronDown, 
  Plus, 
  Settings, 
  Users, 
  Check,
  Loader2
} from 'lucide-react';
import { generateSlugFromName } from '@/types/organization';

export const OrganizationSelector = () => {
  const { currentOrganization, organizations, switchOrganization, isLoading } = useOrganizationContext();
  const { createOrganization, isCreating } = useOrganizations();
  const { toast } = useToast();
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newOrgName, setNewOrgName] = useState('');
  const [newOrgDescription, setNewOrgDescription] = useState('');

  const handleCreateOrganization = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newOrgName.trim()) {
      toast({
        title: 'Name erforderlich',
        description: 'Bitte geben Sie einen Namen für die Organisation ein.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const slug = generateSlugFromName(newOrgName);
      await createOrganization({
        name: newOrgName.trim(),
        slug,
        description: newOrgDescription.trim() || undefined
      });
      
      setNewOrgName('');
      setNewOrgDescription('');
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error('Error creating organization:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground">
        <Loader2 className="h-4 w-4 animate-spin" />
        Lade Organisationen...
      </div>
    );
  }

  if (!currentOrganization) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground">
        <Building2 className="h-4 w-4" />
        Keine Organisation
      </div>
    );
  }

  return (
    <div className="px-3 py-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className="w-full justify-between h-auto p-2 text-left"
          >
            <div className="flex items-center gap-2 min-w-0">
              <Building2 className="h-4 w-4 flex-shrink-0" />
              <div className="min-w-0">
                <div className="font-medium truncate">
                  {currentOrganization.name}
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {currentOrganization.user_role === 'owner' ? 'Eigentümer' : 
                   currentOrganization.user_role === 'admin' ? 'Administrator' : 'Mitglied'}
                </div>
              </div>
            </div>
            <ChevronDown className="h-4 w-4 flex-shrink-0" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="start" className="w-64">
          <DropdownMenuLabel>Organisationen</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {organizations.map((org) => (
            <DropdownMenuItem
              key={org.id}
              onClick={() => switchOrganization(org.id)}
              className="flex items-center gap-2"
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <Building2 className="h-4 w-4 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <div className="font-medium truncate">{org.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {org.user_role === 'owner' ? 'Eigentümer' : 
                     org.user_role === 'admin' ? 'Administrator' : 'Mitglied'}
                  </div>
                </div>
              </div>
              {currentOrganization.id === org.id && (
                <Check className="h-4 w-4 text-primary" />
              )}
            </DropdownMenuItem>
          ))}
          
          <DropdownMenuSeparator />
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <Plus className="h-4 w-4 mr-2" />
                Neue Organisation
              </DropdownMenuItem>
            </DialogTrigger>
            
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Neue Organisation erstellen</DialogTitle>
                <DialogDescription>
                  Erstellen Sie eine neue Organisation für Ihr Team.
                </DialogDescription>
              </DialogHeader>
              
              <form onSubmit={handleCreateOrganization} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="org-name">Organisationsname</Label>
                  <Input
                    id="org-name"
                    value={newOrgName}
                    onChange={(e) => setNewOrgName(e.target.value)}
                    placeholder="Meine Organisation"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="org-description">Beschreibung (optional)</Label>
                  <Input
                    id="org-description"
                    value={newOrgDescription}
                    onChange={(e) => setNewOrgDescription(e.target.value)}
                    placeholder="Kurze Beschreibung der Organisation"
                  />
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsCreateDialogOpen(false)}
                  >
                    Abbrechen
                  </Button>
                  <Button type="submit" disabled={isCreating}>
                    {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Erstellen
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
          
          {currentOrganization.user_role === 'owner' && (
            <>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Organisationseinstellungen
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Users className="h-4 w-4 mr-2" />
                Mitglieder verwalten
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
