import { supabase } from '@/integrations/supabase/client';
import { generateSlugFromName } from '@/types/organization';

interface CreateOrganizationParams {
  organizationName: string;
  organizationDescription?: string;
  userId: string;
  accessToken: string;
}

interface CreateOrganizationResult {
  success: boolean;
  organization?: any;
  error?: string;
}

/**
 * Creates an organization using the secure Edge Function
 */
export const createOrganizationSecure = async (params: CreateOrganizationParams): Promise<CreateOrganizationResult> => {
  try {
    const { data: orgResponse, error: orgError } = await supabase.functions.invoke(
      'create-organization',
      {
        body: {
          organizationName: params.organizationName.trim(),
          organizationDescription: params.organizationDescription || `${params.organizationName} - Freelance Organization`
        },
        headers: {
          Authorization: `Bearer ${params.accessToken}`,
        },
      }
    );

    if (orgError) {
      console.error('Edge function error:', orgError);
      throw new Error(`Edge function failed: ${orgError.message}`);
    }

    if (!orgResponse?.success) {
      throw new Error(orgResponse?.error || 'Failed to create organization');
    }

    return {
      success: true,
      organization: orgResponse.organization
    };
  } catch (error) {
    console.error('Error in createOrganizationSecure:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Fallback: Creates an organization using direct database calls
 * Only used when Edge Function is not available
 */
export const createOrganizationFallback = async (params: CreateOrganizationParams): Promise<CreateOrganizationResult> => {
  try {
    console.log('Using fallback organization creation method');
    
    // Generate unique slug
    const baseSlug = generateSlugFromName(params.organizationName);
    let finalSlug = baseSlug;
    let counter = 0;

    // Check for slug uniqueness
    while (true) {
      const { data: existingOrg } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', finalSlug)
        .single();

      if (!existingOrg) break;

      counter++;
      finalSlug = `${baseSlug}-${counter}`;
    }

    // Create organization
    const { data: orgData, error: orgError } = await supabase
      .from('organizations')
      .insert([{
        name: params.organizationName.trim(),
        slug: finalSlug,
        owner_id: params.userId,
        description: params.organizationDescription || `${params.organizationName} - Freelance Organization`
      }])
      .select()
      .single();

    if (orgError) {
      console.error('Organization creation error:', orgError);
      throw new Error(`Failed to create organization: ${orgError.message}`);
    }

    // Add user as owner
    const { error: memberError } = await supabase
      .from('organization_members')
      .insert([{
        organization_id: orgData.id,
        user_id: params.userId,
        role: 'owner'
      }]);

    if (memberError) {
      console.error('Membership creation error:', memberError);
      
      // Try to clean up the organization
      await supabase
        .from('organizations')
        .delete()
        .eq('id', orgData.id);
      
      throw new Error(`Failed to create organization membership: ${memberError.message}`);
    }

    return {
      success: true,
      organization: orgData
    };
  } catch (error) {
    console.error('Error in createOrganizationFallback:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Main function that tries secure method first, then falls back to direct method
 */
export const createOrganizationWithFallback = async (params: CreateOrganizationParams): Promise<CreateOrganizationResult> => {
  // Try secure Edge Function first
  const secureResult = await createOrganizationSecure(params);
  
  if (secureResult.success) {
    return secureResult;
  }

  console.warn('Secure organization creation failed, trying fallback method:', secureResult.error);
  
  // Fallback to direct database calls
  const fallbackResult = await createOrganizationFallback(params);
  
  if (!fallbackResult.success) {
    console.error('Both secure and fallback methods failed');
  }
  
  return fallbackResult;
};
