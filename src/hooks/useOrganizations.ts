import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { 
  Organization, 
  OrganizationWithRole, 
  CreateOrganizationData, 
  UpdateOrganizationData,
  OrganizationMember,
  InviteMemberData,
  UpdateMemberRoleData
} from '@/types/organization';
import { useToast } from '@/hooks/use-toast';

export const useOrganizations = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all organizations for the current user
  const {
    data: organizations = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['user-organizations'],
    queryFn: async (): Promise<OrganizationWithRole[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get the user's organization memberships
      const { data: memberships, error: memberError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', user.id);

      if (memberError) {
        throw memberError;
      }

      if (!memberships || memberships.length === 0) {
        return [];
      }

      // Get the organization details
      const orgIds = memberships.map(m => m.organization_id);
      const { data: orgs, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .in('id', orgIds)
        .order('name');

      if (orgError) {
        throw orgError;
      }

      // Combine the data
      const mappedOrgs = (orgs || []).map(org => {
        const membership = memberships.find(m => m.organization_id === org.id);
        return {
          ...org,
          user_role: membership?.role as 'owner' | 'admin' | 'member',
          is_owner: membership?.role === 'owner',
          member_count: 1 // We'll calculate this properly later if needed
        };
      });

      return mappedOrgs;


    }
  });

  // Get current user's default organization (first one they own, then first one they're admin of, then first one)
  const {
    data: defaultOrganization,
    isLoading: isLoadingDefault
  } = useQuery({
    queryKey: ['default-organization'],
    queryFn: async (): Promise<string | null> => {
      if (organizations.length === 0) return null;

      // Find first organization where user is owner
      const ownedOrg = organizations.find(org => org.user_role === 'owner');
      if (ownedOrg) return ownedOrg.id;

      // Find first organization where user is admin
      const adminOrg = organizations.find(org => org.user_role === 'admin');
      if (adminOrg) return adminOrg.id;

      // Return first organization
      return organizations[0]?.id || null;
    },
    enabled: organizations.length > 0
  });

  // Create new organization using secure Edge Function
  const createOrganization = useMutation({
    mutationFn: async (orgData: CreateOrganizationData) => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('User not authenticated');

      // Use Edge Function for secure organization creation
      const { data: orgResponse, error: orgError } = await supabase.functions.invoke(
        'create-organization',
        {
          body: {
            organizationName: orgData.name,
            organizationDescription: orgData.description
          },
          headers: {
            Authorization: `Bearer ${session.access_token}`,
          },
        }
      );

      if (orgError || !orgResponse?.success) {
        throw new Error(orgResponse?.error || 'Failed to create organization');
      }

      return orgResponse.organization;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-organizations'] });
      queryClient.invalidateQueries({ queryKey: ['default-organization'] });
      toast({
        title: 'Organisation erstellt',
        description: 'Neue Organisation wurde erfolgreich erstellt.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Organisation konnte nicht erstellt werden.',
        variant: 'destructive'
      });
      console.error('Error creating organization:', error);
    }
  });

  // Update organization
  const updateOrganization = useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateOrganizationData) => {
      const { data, error } = await supabase
        .from('organizations')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-organizations'] });
      toast({
        title: 'Organisation aktualisiert',
        description: 'Organisation wurde erfolgreich aktualisiert.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Organisation konnte nicht aktualisiert werden.',
        variant: 'destructive'
      });
      console.error('Error updating organization:', error);
    }
  });

  // Delete organization
  const deleteOrganization = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('organizations')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-organizations'] });
      queryClient.invalidateQueries({ queryKey: ['default-organization'] });
      toast({
        title: 'Organisation gelöscht',
        description: 'Organisation wurde erfolgreich gelöscht.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Organisation konnte nicht gelöscht werden.',
        variant: 'destructive'
      });
      console.error('Error deleting organization:', error);
    }
  });

  return {
    organizations,
    defaultOrganization,
    isLoading: isLoading || isLoadingDefault,
    error,
    createOrganization: createOrganization.mutateAsync,
    updateOrganization: updateOrganization.mutateAsync,
    deleteOrganization: deleteOrganization.mutateAsync,
    isCreating: createOrganization.isPending,
    isUpdating: updateOrganization.isPending,
    isDeleting: deleteOrganization.isPending
  };
};

export const useOrganizationMembers = (organizationId: string | null) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get organization members
  const {
    data: members = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['organization-members', organizationId],
    queryFn: async (): Promise<OrganizationMember[]> => {
      if (!organizationId) return [];

      const { data, error } = await supabase
        .from('organization_members')
        .select('*')
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data || [];
    },
    enabled: !!organizationId
  });

  // Invite member (placeholder - would need email invitation system)
  const inviteMember = useMutation({
    mutationFn: async (inviteData: InviteMemberData) => {
      // This would typically send an email invitation
      // For now, we'll just add the member directly if they exist
      const { data: userData, error: userError } = await supabase
        .from('auth.users')
        .select('id')
        .eq('email', inviteData.user_email)
        .single();

      if (userError) throw new Error('User not found');

      const { data, error } = await supabase
        .from('organization_members')
        .insert([{
          organization_id: inviteData.organization_id,
          user_id: userData.id,
          role: inviteData.role
        }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization-members', organizationId] });
      toast({
        title: 'Mitglied eingeladen',
        description: 'Neues Mitglied wurde zur Organisation hinzugefügt.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Mitglied konnte nicht eingeladen werden.',
        variant: 'destructive'
      });
      console.error('Error inviting member:', error);
    }
  });

  // Update member role
  const updateMemberRole = useMutation({
    mutationFn: async ({ member_id, role }: UpdateMemberRoleData) => {
      const { data, error } = await supabase
        .from('organization_members')
        .update({ role })
        .eq('id', member_id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization-members', organizationId] });
      toast({
        title: 'Rolle aktualisiert',
        description: 'Mitgliederrolle wurde erfolgreich aktualisiert.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Rolle konnte nicht aktualisiert werden.',
        variant: 'destructive'
      });
      console.error('Error updating member role:', error);
    }
  });

  // Remove member
  const removeMember = useMutation({
    mutationFn: async (memberId: string) => {
      const { error } = await supabase
        .from('organization_members')
        .delete()
        .eq('id', memberId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization-members', organizationId] });
      toast({
        title: 'Mitglied entfernt',
        description: 'Mitglied wurde aus der Organisation entfernt.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Mitglied konnte nicht entfernt werden.',
        variant: 'destructive'
      });
      console.error('Error removing member:', error);
    }
  });

  return {
    members,
    isLoading,
    error,
    inviteMember: inviteMember.mutateAsync,
    updateMemberRole: updateMemberRole.mutateAsync,
    removeMember: removeMember.mutateAsync,
    isInviting: inviteMember.isPending,
    isUpdatingRole: updateMemberRole.isPending,
    isRemoving: removeMember.isPending
  };
};
