import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { OrganizationContextType, OrganizationWithRole } from '@/types/organization';
import { useOrganizations } from '@/hooks/useOrganizations';
import { useToast } from '@/hooks/use-toast';

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

interface OrganizationProviderProps {
  children: ReactNode;
}

export const OrganizationProvider: React.FC<OrganizationProviderProps> = ({ children }) => {
  const [currentOrganization, setCurrentOrganization] = useState<OrganizationWithRole | null>(null);
  const { organizations, defaultOrganization, isLoading, error } = useOrganizations();
  const { toast } = useToast();



  // Set current organization when data loads
  useEffect(() => {
    if (!isLoading && organizations.length > 0) {
      // If no current organization is set, use the default one
      if (!currentOrganization) {
        const defaultOrg = defaultOrganization
          ? organizations.find(org => org.id === defaultOrganization)
          : organizations[0]; // Fallback to first organization

        if (defaultOrg) {
          setCurrentOrganization(defaultOrg);

          // Store in localStorage for persistence
          localStorage.setItem('currentOrganizationId', defaultOrg.id);
        }
      }
    }
  }, [organizations, defaultOrganization, isLoading, currentOrganization]);

  // Load current organization from localStorage on mount
  useEffect(() => {
    const storedOrgId = localStorage.getItem('currentOrganizationId');

    if (storedOrgId && organizations.length > 0 && !currentOrganization) {
      const storedOrg = organizations.find(org => org.id === storedOrgId);

      if (storedOrg) {
        setCurrentOrganization(storedOrg);
      }
    }
  }, [organizations, currentOrganization]);

  const switchOrganization = async (organizationId: string): Promise<void> => {
    const targetOrg = organizations.find(org => org.id === organizationId);
    
    if (!targetOrg) {
      toast({
        title: 'Organisation nicht gefunden',
        description: 'Die ausgewählte Organisation konnte nicht gefunden werden.',
        variant: 'destructive'
      });
      return;
    }

    setCurrentOrganization(targetOrg);
    localStorage.setItem('currentOrganizationId', organizationId);
    
    toast({
      title: 'Organisation gewechselt',
      description: `Sie arbeiten jetzt in "${targetOrg.name}".`,
    });
  };

  const refreshOrganizations = async (): Promise<void> => {
    // This would trigger a refetch of organizations
    // The useOrganizations hook handles the actual refetching
    window.location.reload(); // Simple approach for now
  };

  const contextValue: OrganizationContextType = {
    currentOrganization,
    organizations,
    isLoading,
    error,
    switchOrganization,
    refreshOrganizations
  };

  return (
    <OrganizationContext.Provider value={contextValue}>
      {children}
    </OrganizationContext.Provider>
  );
};

export const useOrganizationContext = (): OrganizationContextType => {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganizationContext must be used within an OrganizationProvider');
  }
  return context;
};

// Hook to get current organization ID (useful for queries)
export const useCurrentOrganizationId = (): string | null => {
  const { currentOrganization } = useOrganizationContext();
  return currentOrganization?.id || null;
};
