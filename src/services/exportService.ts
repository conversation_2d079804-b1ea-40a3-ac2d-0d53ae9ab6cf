import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';
import { FreelanceProject } from '@/types/freelance';
import { UserSettings } from '@/types/settings';
import {
  ExportData,
  ExportOptions,
  ExportResult,
  PDFExportConfig,
  ExcelExportConfig,
  DEFAULT_PDF_CONFIG,
  DEFAULT_EXCEL_CONFIG
} from '@/types/export';
import { STATUS_LABELS, WORK_LOCATION_LABELS } from '@/types/freelance';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

export class ExportService {
  /**
   * Generate PDF export of project data
   */
  static async generatePDF(
    projects: FreelanceProject[],
    settings?: UserSettings | null,
    options: Partial<ExportOptions> = {},
    config: Partial<PDFExportConfig> = {}
  ): Promise<ExportResult> {
    try {
      // Validate input
      if (!projects || projects.length === 0) {
        return {
          success: false,
          error: 'Keine Projekte zum Exportieren vorhanden.',
        };
      }

      const pdfConfig = { ...DEFAULT_PDF_CONFIG, ...config };
      const doc = new jsPDF();
      
      // Set document properties
      doc.setProperties({
        title: pdfConfig.title,
        author: pdfConfig.author,
        subject: pdfConfig.subject,
        keywords: pdfConfig.keywords,
      });

      let yPosition = pdfConfig.margins.top;
      const pageWidth = doc.internal.pageSize.width;
      const contentWidth = pageWidth - pdfConfig.margins.left - pdfConfig.margins.right;

      // Title
      doc.setFontSize(pdfConfig.fontSize.title);
      doc.setTextColor(pdfConfig.colors.primary);
      doc.text(pdfConfig.title, pdfConfig.margins.left, yPosition);
      yPosition += 15;

      // Export info
      doc.setFontSize(pdfConfig.fontSize.small);
      doc.setTextColor(pdfConfig.colors.secondary);
      const exportDate = format(new Date(), 'dd.MM.yyyy HH:mm', { locale: de });
      doc.text(`Exportiert am: ${exportDate}`, pdfConfig.margins.left, yPosition);
      yPosition += 10;
      doc.text(`Anzahl Projekte: ${projects.length}`, pdfConfig.margins.left, yPosition);
      yPosition += 20;

      // User settings section (if included and available)
      if (options.includeSettings && settings) {
        yPosition = this.addUserSettingsSection(doc, settings, yPosition, pdfConfig, contentWidth);
        yPosition += 15;
      }

      // Projects section
      doc.setFontSize(pdfConfig.fontSize.heading);
      doc.setTextColor(pdfConfig.colors.primary);
      doc.text('Projekte', pdfConfig.margins.left, yPosition);
      yPosition += 10;

      // Process each project
      for (let i = 0; i < projects.length; i++) {
        const project = projects[i];
        
        // Check if we need a new page
        if (yPosition > doc.internal.pageSize.height - 60) {
          doc.addPage();
          yPosition = pdfConfig.margins.top;
        }

        yPosition = this.addProjectSection(doc, project, yPosition, pdfConfig, contentWidth, options);
        
        // Add separator between projects (except for the last one)
        if (i < projects.length - 1) {
          yPosition += 5;
          doc.setDrawColor(pdfConfig.colors.border);
          doc.line(pdfConfig.margins.left, yPosition, pageWidth - pdfConfig.margins.right, yPosition);
          yPosition += 10;
        }
      }

      // Generate filename
      const timestamp = format(new Date(), 'yyyy-MM-dd');
      const fileName = `freelance-projects-${timestamp}.pdf`;

      // Save the PDF
      doc.save(fileName);

      return {
        success: true,
        fileName,
      };
    } catch (error) {
      console.error('Error generating PDF:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler beim PDF-Export',
      };
    }
  }

  /**
   * Add user settings section to PDF
   */
  private static addUserSettingsSection(
    doc: jsPDF,
    settings: UserSettings,
    yPosition: number,
    config: PDFExportConfig,
    contentWidth: number
  ): number {
    doc.setFontSize(config.fontSize.heading);
    doc.setTextColor(config.colors.primary);
    doc.text('Benutzereinstellungen', config.margins.left, yPosition);
    yPosition += 10;

    doc.setFontSize(config.fontSize.body);
    doc.setTextColor(config.colors.text);

    const settingsData = [
      ['Name:', settings.full_name || 'Nicht angegeben'],
      ['E-Mail:', settings.professional_email || 'Nicht angegeben'],
      ['Telefon:', settings.phone || 'Nicht angegeben'],
      ['Adresse:', settings.address || 'Nicht angegeben'],
      ['Website:', settings.website || 'Nicht angegeben'],
      ['Stundensatz:', settings.hourly_rate_eur ? `${settings.hourly_rate_eur} €/h` : 'Nicht angegeben'],
      ['Verfügbarkeit:', this.formatAvailability(settings)],
    ];

    settingsData.forEach(([label, value]) => {
      if (yPosition > doc.internal.pageSize.height - 40) {
        doc.addPage();
        yPosition = config.margins.top;
      }
      
      doc.setFont(undefined, 'bold');
      doc.text(label, config.margins.left, yPosition);
      doc.setFont(undefined, 'normal');
      
      const labelWidth = doc.getTextWidth(label);
      const valueText = doc.splitTextToSize(value, contentWidth - labelWidth - 5);
      doc.text(valueText, config.margins.left + labelWidth + 5, yPosition);
      
      yPosition += Array.isArray(valueText) ? valueText.length * 5 + 2 : 7;
    });

    return yPosition;
  }

  /**
   * Add project section to PDF
   */
  private static addProjectSection(
    doc: jsPDF,
    project: FreelanceProject,
    yPosition: number,
    config: PDFExportConfig,
    contentWidth: number,
    options: Partial<ExportOptions>
  ): number {
    // Project title
    doc.setFontSize(config.fontSize.heading);
    doc.setTextColor(config.colors.primary);
    const titleText = doc.splitTextToSize(project.project_name, contentWidth);
    doc.text(titleText, config.margins.left, yPosition);
    yPosition += Array.isArray(titleText) ? titleText.length * 6 + 5 : 10;

    doc.setFontSize(config.fontSize.body);
    doc.setTextColor(config.colors.text);

    // Project details
    const projectData = [
      ['Unternehmen:', project.company_name],
      ['Status:', STATUS_LABELS[project.status as keyof typeof STATUS_LABELS] || project.status],
      ['Kontaktperson:', project.contact_person || 'Nicht angegeben'],
      ['E-Mail:', project.contact_email || 'Nicht angegeben'],
      ['Telefon:', project.contact_phone || 'Nicht angegeben'],
      ['Budget:', project.budget_range || 'Nicht angegeben'],
      ['Projektstart:', project.project_start_date ? format(new Date(project.project_start_date), 'dd.MM.yyyy', { locale: de }) : 'Nicht angegeben'],
      ['Projektende:', project.project_end_date ? format(new Date(project.project_end_date), 'dd.MM.yyyy', { locale: de }) : 'Nicht angegeben'],
      ['Bewerbungsdatum:', project.application_date ? format(new Date(project.application_date), 'dd.MM.yyyy', { locale: de }) : 'Nicht angegeben'],
      ['Arbeitsort:', project.work_location_type ? WORK_LOCATION_LABELS[project.work_location_type as keyof typeof WORK_LOCATION_LABELS] : 'Nicht angegeben'],
      ['Remote-Anteil:', project.remote_percentage ? `${project.remote_percentage}%` : 'Nicht angegeben'],
      ['Quelle:', project.source || 'Nicht angegeben'],
      ['URL:', project.listing_url || 'Nicht angegeben'],
    ];

    // Add skills
    if (project.required_skills && project.required_skills.length > 0) {
      projectData.push(['Skills:', project.required_skills.join(', ')]);
    } else {
      projectData.push(['Skills:', 'Nicht angegeben']);
    }

    // Add project details
    projectData.forEach(([label, value]) => {
      if (yPosition > doc.internal.pageSize.height - 40) {
        doc.addPage();
        yPosition = config.margins.top;
      }
      
      doc.setFont(undefined, 'bold');
      doc.text(label, config.margins.left, yPosition);
      doc.setFont(undefined, 'normal');
      
      const labelWidth = doc.getTextWidth(label);
      const valueText = doc.splitTextToSize(value, contentWidth - labelWidth - 5);
      doc.text(valueText, config.margins.left + labelWidth + 5, yPosition);
      
      yPosition += Array.isArray(valueText) ? valueText.length * 5 + 2 : 7;
    });

    // Add description if available
    if (project.project_description) {
      yPosition += 5;
      doc.setFont(undefined, 'bold');
      doc.text('Beschreibung:', config.margins.left, yPosition);
      yPosition += 7;
      
      doc.setFont(undefined, 'normal');
      const descriptionText = doc.splitTextToSize(project.project_description, contentWidth);
      doc.text(descriptionText, config.margins.left, yPosition);
      yPosition += Array.isArray(descriptionText) ? descriptionText.length * 5 + 5 : 10;
    }

    // Add application text if included and available
    if (options.includeApplicationText && project.application_text) {
      yPosition += 5;
      doc.setFont(undefined, 'bold');
      doc.text('Bewerbungstext:', config.margins.left, yPosition);
      yPosition += 7;
      
      doc.setFont(undefined, 'normal');
      const applicationText = doc.splitTextToSize(project.application_text, contentWidth);
      doc.text(applicationText, config.margins.left, yPosition);
      yPosition += Array.isArray(applicationText) ? applicationText.length * 5 + 5 : 10;
    }

    // Add notes if included and available
    if (options.includeNotes && project.notes) {
      yPosition += 5;
      doc.setFont(undefined, 'bold');
      doc.text('Notizen:', config.margins.left, yPosition);
      yPosition += 7;
      
      doc.setFont(undefined, 'normal');
      const notesText = doc.splitTextToSize(project.notes, contentWidth);
      doc.text(notesText, config.margins.left, yPosition);
      yPosition += Array.isArray(notesText) ? notesText.length * 5 + 5 : 10;
    }

    return yPosition + 10;
  }

  /**
   * Generate Excel export of project data
   */
  static async generateExcel(
    projects: FreelanceProject[],
    settings?: UserSettings | null,
    options: Partial<ExportOptions> = {},
    config: Partial<ExcelExportConfig> = {}
  ): Promise<ExportResult> {
    try {
      // Validate input
      if (!projects || projects.length === 0) {
        return {
          success: false,
          error: 'Keine Projekte zum Exportieren vorhanden.',
        };
      }

      const excelConfig = { ...DEFAULT_EXCEL_CONFIG, ...config };

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Prepare project data for Excel
      const projectData = projects.map(project => ({
        'Projektname': project.project_name || '',
        'Unternehmen': project.company_name || '',
        'Status': STATUS_LABELS[project.status as keyof typeof STATUS_LABELS] || project.status,
        'Kontaktperson': project.contact_person || '',
        'E-Mail': project.contact_email || '',
        'Telefon': project.contact_phone || '',
        'Budget': project.budget_range || '',
        'Projektstart': project.project_start_date ? format(new Date(project.project_start_date), 'dd.MM.yyyy', { locale: de }) : '',
        'Projektende': project.project_end_date ? format(new Date(project.project_end_date), 'dd.MM.yyyy', { locale: de }) : '',
        'Bewerbungsdatum': project.application_date ? format(new Date(project.application_date), 'dd.MM.yyyy', { locale: de }) : '',
        'Arbeitsort': project.work_location_type ? WORK_LOCATION_LABELS[project.work_location_type as keyof typeof WORK_LOCATION_LABELS] : '',
        'Remote-Anteil (%)': project.remote_percentage || '',
        'Arbeitsort-Notizen': project.work_location_notes || '',
        'Skills': project.required_skills ? project.required_skills.join(', ') : '',
        'Beschreibung': project.project_description || '',
        'Quelle': project.source || '',
        'URL': project.listing_url || '',
        'Erstellt am': project.created_at ? format(new Date(project.created_at), 'dd.MM.yyyy HH:mm', { locale: de }) : '',
        'Aktualisiert am': project.updated_at ? format(new Date(project.updated_at), 'dd.MM.yyyy HH:mm', { locale: de }) : '',
        ...(options.includeApplicationText && { 'Bewerbungstext': project.application_text || '' }),
        ...(options.includeNotes && { 'Notizen': project.notes || '' }),
      }));

      // Create worksheet from project data
      const worksheet = XLSX.utils.json_to_sheet(projectData);

      // Set column widths for better readability
      const columnWidths = [
        { wch: 25 }, // Projektname
        { wch: 20 }, // Unternehmen
        { wch: 15 }, // Status
        { wch: 20 }, // Kontaktperson
        { wch: 25 }, // E-Mail
        { wch: 15 }, // Telefon
        { wch: 15 }, // Budget
        { wch: 12 }, // Projektstart
        { wch: 12 }, // Projektende
        { wch: 15 }, // Bewerbungsdatum
        { wch: 12 }, // Arbeitsort
        { wch: 10 }, // Remote-Anteil
        { wch: 20 }, // Arbeitsort-Notizen
        { wch: 30 }, // Skills
        { wch: 40 }, // Beschreibung
        { wch: 15 }, // Quelle
        { wch: 30 }, // URL
        { wch: 15 }, // Erstellt am
        { wch: 15 }, // Aktualisiert am
      ];

      // Add additional columns if options are enabled
      if (options.includeApplicationText) {
        columnWidths.push({ wch: 50 }); // Bewerbungstext
      }
      if (options.includeNotes) {
        columnWidths.push({ wch: 30 }); // Notizen
      }

      worksheet['!cols'] = columnWidths;

      // Apply header styling if configured
      if (excelConfig.headerStyle) {
        const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
          if (!worksheet[cellAddress]) continue;

          worksheet[cellAddress].s = {
            font: excelConfig.headerStyle.font,
            fill: excelConfig.headerStyle.fill,
            border: excelConfig.headerStyle.border ? {
              top: { style: 'thin' },
              bottom: { style: 'thin' },
              left: { style: 'thin' },
              right: { style: 'thin' }
            } : undefined
          };
        }
      }

      // Freeze header row if configured
      if (excelConfig.freezeHeader) {
        worksheet['!freeze'] = { xSplit: 0, ySplit: 1 };
      }

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, excelConfig.sheetName);

      // Add settings sheet if requested and available
      if (options.includeSettings && settings) {
        const settingsData = [
          { 'Einstellung': 'Name', 'Wert': settings.full_name || 'Nicht angegeben' },
          { 'Einstellung': 'E-Mail', 'Wert': settings.professional_email || 'Nicht angegeben' },
          { 'Einstellung': 'Telefon', 'Wert': settings.phone || 'Nicht angegeben' },
          { 'Einstellung': 'Adresse', 'Wert': settings.address || 'Nicht angegeben' },
          { 'Einstellung': 'Website', 'Wert': settings.website || 'Nicht angegeben' },
          { 'Einstellung': 'Stundensatz', 'Wert': settings.hourly_rate_eur ? `${settings.hourly_rate_eur} €/h` : 'Nicht angegeben' },
          { 'Einstellung': 'Verfügbarkeit', 'Wert': this.formatAvailability(settings) },
        ];

        const settingsWorksheet = XLSX.utils.json_to_sheet(settingsData);
        settingsWorksheet['!cols'] = [{ wch: 20 }, { wch: 40 }];
        XLSX.utils.book_append_sheet(workbook, settingsWorksheet, 'Einstellungen');
      }

      // Generate filename
      const timestamp = format(new Date(), 'yyyy-MM-dd');
      const fileName = `freelance-projects-${timestamp}.xlsx`;

      // Write and download the file
      XLSX.writeFile(workbook, fileName);

      return {
        success: true,
        fileName,
      };
    } catch (error) {
      console.error('Error generating Excel:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler beim Excel-Export',
      };
    }
  }

  /**
   * Format availability information
   */
  private static formatAvailability(settings: UserSettings): string {
    const parts = [];

    if (settings.availability_start_date) {
      parts.push(`ab ${format(new Date(settings.availability_start_date), 'dd.MM.yyyy', { locale: de })}`);
    }

    if (settings.availability_end_date) {
      parts.push(`bis ${format(new Date(settings.availability_end_date), 'dd.MM.yyyy', { locale: de })}`);
    }

    if (settings.availability_hours_per_week) {
      parts.push(`${settings.availability_hours_per_week}h/Woche`);
    }

    if (settings.availability_notes) {
      parts.push(settings.availability_notes);
    }

    return parts.length > 0 ? parts.join(', ') : 'Nicht angegeben';
  }
}
