import { Tables } from '@/integrations/supabase/types';

// Organization types based on Supabase generated types
export type Organization = Tables<'organizations'>;
export type OrganizationMember = Tables<'organization_members'>;

// Extended types with additional computed properties
export interface OrganizationWithRole extends Organization {
  user_role: 'owner' | 'admin' | 'member';
  is_owner: boolean;
  member_count?: number;
}

export interface OrganizationMemberWithDetails extends OrganizationMember {
  user_email?: string;
  user_name?: string;
  organization_name?: string;
}

// Role definitions
export type OrganizationRole = 'owner' | 'admin' | 'member';

export const ORGANIZATION_ROLES: Record<OrganizationRole, { label: string; description: string }> = {
  owner: {
    label: 'Owner',
    description: 'Full access to organization settings and can manage all members'
  },
  admin: {
    label: 'Admin', 
    description: 'Can manage projects and invite members'
  },
  member: {
    label: 'Member',
    description: 'Can view and create projects'
  }
};

// Form data types
export interface CreateOrganizationData {
  name: string;
  slug: string;
  description?: string;
}

export interface UpdateOrganizationData {
  id: string;
  name?: string;
  slug?: string;
  description?: string;
}

export interface InviteMemberData {
  organization_id: string;
  user_email: string;
  role: OrganizationRole;
}

export interface UpdateMemberRoleData {
  member_id: string;
  role: OrganizationRole;
}

// Context types
export interface OrganizationContextType {
  currentOrganization: OrganizationWithRole | null;
  organizations: OrganizationWithRole[];
  isLoading: boolean;
  error: Error | null;
  switchOrganization: (organizationId: string) => Promise<void>;
  refreshOrganizations: () => Promise<void>;
}

// API response types
export interface OrganizationListResponse {
  organizations: OrganizationWithRole[];
}

export interface OrganizationMembersResponse {
  members: OrganizationMemberWithDetails[];
  total: number;
}

// Utility functions
export const canManageMembers = (role: OrganizationRole): boolean => {
  return role === 'owner' || role === 'admin';
};

export const canDeleteOrganization = (role: OrganizationRole): boolean => {
  return role === 'owner';
};

export const canUpdateOrganization = (role: OrganizationRole): boolean => {
  return role === 'owner' || role === 'admin';
};

export const generateSlugFromName = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
    .substring(0, 50); // Limit length
};
