-- Debug queries to check organization setup <NAME_EMAIL>

-- 1. Check if user exists and get their ID
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';

-- 2. Check organizations table
SELECT * FROM public.organizations;

-- 3. Check organization_members table
SELECT * FROM public.organization_members;

-- 4. Check if user has organization membership
SELECT 
  u.email,
  o.name as org_name,
  o.slug as org_slug,
  om.role
FROM auth.users u
JOIN public.organization_members om ON u.id = om.user_id
JOIN public.organizations o ON om.organization_id = o.id
WHERE u.email = '<EMAIL>';

-- 5. Check freelance_projects with organization_id
SELECT 
  project_name,
  organization_id,
  user_id
FROM public.freelance_projects
WHERE user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');

-- 6. Test the query that the frontend is using (simulating RLS)
-- This simulates what happens when the user is authenticated
SET LOCAL "request.jwt.claims" = '{"sub": "' || (SELECT id FROM auth.users WHERE email = '<EMAIL>') || '"}';

-- Test the organizations query with RLS
SELECT 
  o.*,
  om.role,
  om.user_id
FROM public.organizations o
JOIN public.organization_members om ON o.id = om.organization_id
WHERE om.user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');

-- 7. Check RLS policies
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual 
FROM pg_policies 
WHERE tablename IN ('organizations', 'organization_members', 'freelance_projects')
ORDER BY tablename, policyname;
