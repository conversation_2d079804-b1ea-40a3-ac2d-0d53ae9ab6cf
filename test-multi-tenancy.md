# Multi-Tenancy Testing Guide

## Quick Test Scenarios

### Test 1: New User Registration
1. Open http://localhost:8080
2. Click "Registrieren" tab
3. Fill in:
   - Email: <EMAIL>
   - Name: Test User
   - Organization: Test Organization
   - Password: password123
   - Confirm Password: password123
4. Click "Registrieren"
5. **Expected**: Auto-login, organization created, dashboard loads

### Test 2: Organization Context
1. After registration, check sidebar
2. **Expected**: Organization selector shows "Test Organization"
3. Check dashboard header
4. **Expected**: Organization name displayed next to Freelancify

### Test 3: Project Creation
1. Click "Neues Projekt" button
2. Create a test project
3. **Expected**: Project appears in dashboard
4. Check database: project should have organization_id

### Test 4: Settings Page
1. Navigate to Settings
2. **Expected**: Organization section shows:
   - Current organization name
   - User role (Eigentümer)
   - Organization list

### Test 5: Data Isolation (Advanced)
1. Create second user with different organization
2. <PERSON><PERSON> as second user
3. **Expected**: Cannot see first user's projects
4. Create project as second user
5. **Expected**: First user cannot see second user's project

## Database Verification Queries

```sql
-- Check organizations were created
SELECT * FROM organizations;

-- Check organization members
SELECT * FROM organization_members;

-- Check projects have organization_id
SELECT project_name, organization_id FROM freelance_projects;

-- Verify RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('organizations', 'organization_members', 'freelance_projects');
```

## Common Issues & Solutions

### Issue: Organization not loading
**Solution**: Check browser console for errors, verify database migrations applied

### Issue: Projects not showing
**Solution**: Verify organization context is set, check RLS policies

### Issue: Registration fails
**Solution**: Check unique constraints, verify organization name/slug generation

### Issue: Organization switching not working
**Solution**: Check localStorage, verify organization membership

## Success Indicators

✅ New users can register with organization creation
✅ Organization selector appears in sidebar
✅ Dashboard shows organization context
✅ Projects are organization-scoped
✅ Settings page shows organization info
✅ Data isolation works between organizations
✅ No console errors or warnings
✅ Smooth user experience throughout

## Next Steps After Testing

1. **Production Deployment**
   - Apply migrations to production database
   - Deploy updated application code
   - Monitor for issues

2. **User Communication**
   - Inform existing users about new organization features
   - Provide guidance on organization management
   - Collect feedback for improvements

3. **Feature Enhancements**
   - Implement email invitations
   - Add organization member management
   - Create organization settings page
   - Add role-based permissions
