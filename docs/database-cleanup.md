# Database Cleanup Guide

## Overview
This guide provides commands to completely clean up the database before applying the new consolidated schema.

## Complete Cleanup Commands

### Option 1: Drop All Multi-Tenancy Objects (Recommended)
```sql
-- Drop all views
DROP VIEW IF EXISTS public.user_organizations_secure;

-- Drop all triggers
DROP TRIGGER IF EXISTS create_default_organization_trigger ON auth.users;
DROP TRIGGER IF EXISTS update_organizations_updated_at ON public.organizations;
DROP TRIGGER IF EXISTS update_organization_members_updated_at ON public.organization_members;
DROP TRIGGER IF EXISTS update_freelance_projects_updated_at ON public.freelance_projects;
DROP TRIGGER IF EXISTS update_user_settings_updated_at ON public.user_settings;

-- Drop all functions
DROP FUNCTION IF EXISTS public.create_default_organization_for_user();
DROP FUNCTION IF EXISTS public.user_has_organization_access(UUID, TEXT);
DROP FUNCTION IF EXISTS public.user_has_organization_access(UUID);
DROP FUNCTION IF EXISTS public.user_can_access_organization(UUID);
DROP FUNCTION IF EXISTS public.get_user_default_organization();
DROP FUNCTION IF EXISTS public.log_security_violation(TEXT, TEXT, UUID, UUID, TEXT);
DROP FUNCTION IF EXISTS public.update_updated_at_column();
DROP FUNCTION IF EXISTS create_policy_if_not_exists(TEXT, TEXT, TEXT, TEXT, TEXT);

-- Drop all RLS policies (organizations)
DROP POLICY IF EXISTS "owners_can_view_own_orgs" ON public.organizations;
DROP POLICY IF EXISTS "owners_can_update_own_orgs" ON public.organizations;
DROP POLICY IF EXISTS "authenticated_users_can_create_orgs" ON public.organizations;
DROP POLICY IF EXISTS "owners_can_delete_own_orgs" ON public.organizations;
DROP POLICY IF EXISTS "secure_org_access" ON public.organizations;
DROP POLICY IF EXISTS "organizations_select_policy" ON public.organizations;
DROP POLICY IF EXISTS "organizations_insert_policy" ON public.organizations;
DROP POLICY IF EXISTS "organizations_update_policy" ON public.organizations;
DROP POLICY IF EXISTS "organizations_delete_policy" ON public.organizations;

-- Drop all RLS policies (organization_members)
DROP POLICY IF EXISTS "users_can_view_own_memberships" ON public.organization_members;
DROP POLICY IF EXISTS "owners_can_view_org_memberships" ON public.organization_members;
DROP POLICY IF EXISTS "authenticated_users_can_create_memberships" ON public.organization_members;
DROP POLICY IF EXISTS "users_can_update_own_memberships" ON public.organization_members;
DROP POLICY IF EXISTS "owners_can_manage_org_memberships" ON public.organization_members;
DROP POLICY IF EXISTS "users_can_delete_own_memberships" ON public.organization_members;
DROP POLICY IF EXISTS "secure_membership_access" ON public.organization_members;
DROP POLICY IF EXISTS "organization_members_select_policy" ON public.organization_members;
DROP POLICY IF EXISTS "organization_members_insert_policy" ON public.organization_members;
DROP POLICY IF EXISTS "organization_members_update_policy" ON public.organization_members;
DROP POLICY IF EXISTS "organization_members_delete_policy" ON public.organization_members;

-- Drop all RLS policies (freelance_projects)
DROP POLICY IF EXISTS "users_can_view_own_org_projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "users_can_create_own_org_projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "users_can_update_own_org_projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "users_can_delete_own_org_projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "secure_project_access" ON public.freelance_projects;
DROP POLICY IF EXISTS "secure_project_insert" ON public.freelance_projects;
DROP POLICY IF EXISTS "secure_project_update" ON public.freelance_projects;
DROP POLICY IF EXISTS "secure_project_delete" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_select_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_insert_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_update_policy" ON public.freelance_projects;
DROP POLICY IF EXISTS "freelance_projects_delete_policy" ON public.freelance_projects;

-- Drop all RLS policies (user_settings)
DROP POLICY IF EXISTS "users_can_view_own_settings" ON public.user_settings;
DROP POLICY IF EXISTS "users_can_update_own_settings" ON public.user_settings;
DROP POLICY IF EXISTS "users_can_insert_own_settings" ON public.user_settings;
DROP POLICY IF EXISTS "users_can_delete_own_settings" ON public.user_settings;
DROP POLICY IF EXISTS "user_settings_select_policy" ON public.user_settings;
DROP POLICY IF EXISTS "user_settings_insert_policy" ON public.user_settings;
DROP POLICY IF EXISTS "user_settings_update_policy" ON public.user_settings;
DROP POLICY IF EXISTS "user_settings_delete_policy" ON public.user_settings;

-- Drop constraints
ALTER TABLE IF EXISTS public.freelance_projects DROP CONSTRAINT IF EXISTS freelance_projects_organization_required;
ALTER TABLE IF EXISTS public.freelance_projects DROP CONSTRAINT IF EXISTS freelance_projects_organization_id_fkey;
ALTER TABLE IF EXISTS public.organization_members DROP CONSTRAINT IF EXISTS organization_members_organization_id_fkey;
ALTER TABLE IF EXISTS public.organization_members DROP CONSTRAINT IF EXISTS organization_members_user_id_fkey;
ALTER TABLE IF EXISTS public.organizations DROP CONSTRAINT IF EXISTS organizations_owner_id_fkey;

-- Drop indexes
DROP INDEX IF EXISTS public.idx_organizations_owner_id;
DROP INDEX IF EXISTS public.idx_organizations_slug;
DROP INDEX IF EXISTS public.idx_organizations_created_at;
DROP INDEX IF EXISTS public.idx_organization_members_org_id;
DROP INDEX IF EXISTS public.idx_organization_members_user_id;
DROP INDEX IF EXISTS public.idx_organization_members_role;
DROP INDEX IF EXISTS public.idx_freelance_projects_org_id;
DROP INDEX IF EXISTS public.idx_freelance_projects_user_id;
DROP INDEX IF EXISTS public.idx_freelance_projects_status;
DROP INDEX IF EXISTS public.idx_freelance_projects_priority;
DROP INDEX IF EXISTS public.idx_freelance_projects_created_at;
DROP INDEX IF EXISTS public.idx_freelance_projects_deadline;
DROP INDEX IF EXISTS public.idx_freelance_projects_org_status;
DROP INDEX IF EXISTS public.idx_freelance_projects_org_user;
DROP INDEX IF EXISTS public.idx_user_settings_user_id;

-- Remove organization_id column from freelance_projects
ALTER TABLE IF EXISTS public.freelance_projects DROP COLUMN IF EXISTS organization_id;

-- Drop tables (in correct order due to foreign key dependencies)
DROP TABLE IF EXISTS public.organization_members;
DROP TABLE IF EXISTS public.organizations;
DROP TABLE IF EXISTS public.user_settings;

-- Disable RLS on remaining tables
ALTER TABLE IF EXISTS public.freelance_projects DISABLE ROW LEVEL SECURITY;
```

### Option 2: Complete Database Reset (Nuclear Option)
```sql
-- WARNING: This will delete ALL data!
-- Only use this for development/testing environments

-- Drop all tables in public schema
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;

-- Restore default permissions
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;
```

### Option 3: Supabase Reset (If using Supabase)
```bash
# Reset the entire Supabase project (WARNING: Deletes all data!)
npx supabase db reset

# Or reset just the database
npx supabase db reset --db-url "your-database-url"
```

## After Cleanup

### Apply New Schema
```bash
# Apply the consolidated schema
psql -d your_database -f supabase/migrations/001_complete_schema.sql

# Or using Supabase CLI
npx supabase db push
```

### Verify Setup
```sql
-- Check tables were created
SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;

-- Check RLS is enabled
SELECT tablename, rowsecurity FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

-- Check policies exist
SELECT tablename, COUNT(*) as policy_count 
FROM pg_policies 
WHERE schemaname = 'public' 
GROUP BY tablename 
ORDER BY tablename;

-- Check functions exist
SELECT proname FROM pg_proc 
WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
AND proname LIKE '%organization%' OR proname LIKE '%update_updated_at%';

-- Check triggers exist
SELECT trigger_name, event_object_table 
FROM information_schema.triggers 
WHERE trigger_schema = 'public' 
ORDER BY event_object_table, trigger_name;
```

## Troubleshooting

### Permission Errors
```sql
-- If you get permission errors, run as superuser:
-- Connect as postgres user or database owner
```

### Dependency Errors
```sql
-- If foreign key constraints prevent dropping:
-- Drop tables in reverse dependency order:
DROP TABLE IF EXISTS public.freelance_projects;
DROP TABLE IF EXISTS public.organization_members;
DROP TABLE IF EXISTS public.organizations;
DROP TABLE IF EXISTS public.user_settings;
```

### RLS Policy Conflicts
```sql
-- If policies conflict during recreation:
-- Disable RLS temporarily
ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.freelance_projects DISABLE ROW LEVEL SECURITY;

-- Then apply new schema which will re-enable RLS
```

## Best Practices

1. **Backup First**: Always backup your database before cleanup
2. **Test Environment**: Test cleanup and schema application in development first
3. **Incremental Approach**: Use Option 1 (selective cleanup) for production
4. **Verify Results**: Always run verification queries after cleanup
5. **Document Changes**: Keep track of what was cleaned up for rollback purposes

## Recovery

If something goes wrong:

1. **Restore from Backup**: Use your database backup
2. **Revert Migration**: If using version control, revert to previous state
3. **Manual Recreation**: Manually recreate only the objects you need

Remember: The new consolidated schema is designed to be idempotent, so you can safely run it multiple times without issues.
