# Freelancify Database Schema Documentation

## Overview
This document describes the complete database schema for the Freelancify application, a multi-tenant freelance project management system with organization-based data isolation.

## Architecture

### Multi-Tenancy Model
- **Organization-based isolation**: Each organization represents a separate tenant
- **Complete data separation**: Users can only access data from organizations they belong to
- **Role-based access control**: Support for owner, admin, and member roles
- **Secure by default**: Row Level Security (RLS) enforces all access controls

## Tables

### 1. organizations
**Purpose**: Core entity for multi-tenancy. Each organization represents a separate tenant.

**Columns**:
- `id` (UUID, PK): Unique identifier
- `name` (TEXT, NOT NULL): Organization display name
- `slug` (TEXT, UNIQUE, NOT NULL): URL-friendly identifier
- `description` (TEXT): Optional description
- `owner_id` (UUID, FK → auth.users): Organization owner
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps

**Constraints**:
- Name and slug must not be empty
- Slug must be unique across all organizations
- Owner must be a valid user

### 2. organization_members
**Purpose**: Manages user-organization relationships and roles.

**Columns**:
- `id` (UUID, PK): Unique identifier
- `organization_id` (UUID, FK → organizations): Organization reference
- `user_id` (UUID, FK → auth.users): User reference
- `role` (TEXT): Role within organization ('owner', 'admin', 'member')
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps

**Constraints**:
- Unique combination of organization_id and user_id
- Role must be one of: 'owner', 'admin', 'member'

### 3. user_settings
**Purpose**: Stores user-specific preferences and profile data (not organization-specific).

**Columns**:
- `id` (UUID, PK): Unique identifier
- `user_id` (UUID, FK → auth.users, UNIQUE): User reference
- `full_name` (TEXT): User's full name
- `avatar_url` (TEXT): Profile picture URL
- `timezone` (TEXT): User's timezone (default: 'UTC')
- `language` (TEXT): Preferred language (default: 'de')
- `email_notifications` (BOOLEAN): Email notification preference (default: true)
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps

### 4. freelance_projects
**Purpose**: Core business entity representing freelance projects.

**Columns**:
- `id` (UUID, PK): Unique identifier
- `organization_id` (UUID, FK → organizations, NOT NULL): Organization reference
- `user_id` (UUID, FK → auth.users): Project creator/owner
- `title` (TEXT, NOT NULL): Project title
- `description` (TEXT): Project description
- `client_name`, `client_email`, `client_phone` (TEXT): Client information
- `project_url` (TEXT): Project website/URL
- `budget` (DECIMAL(10,2)): Project budget
- `currency` (TEXT): Currency code (default: 'EUR')
- `status` (TEXT): Project status ('draft', 'active', 'completed', 'cancelled')
- `priority` (TEXT): Priority level ('low', 'medium', 'high', 'urgent')
- `start_date`, `end_date`, `deadline` (DATE): Project dates
- `tags` (TEXT[]): Project tags array
- `notes` (TEXT): Additional notes
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps

**Constraints**:
- Title must not be empty
- End date must be after start date (if both set)
- Deadline must be in the future (if set)
- Budget must be positive (if set)
- Status must be valid enum value
- Priority must be valid enum value

## Indexes

### Performance Indexes
- **organizations**: owner_id, slug, created_at
- **organization_members**: organization_id, user_id, role
- **user_settings**: user_id
- **freelance_projects**: organization_id, user_id, status, priority, created_at, deadline

### Composite Indexes
- **freelance_projects**: (organization_id, status), (organization_id, user_id)

## Security (Row Level Security)

### organizations
- **SELECT**: Users can view organizations they own or are members of
- **INSERT**: Users can create organizations (become owner)
- **UPDATE**: Organization owners can update their organizations
- **DELETE**: Organization owners can delete their organizations

### organization_members
- **SELECT**: Users can view their own memberships + owners can view all memberships in their organizations
- **INSERT**: Users can create their own memberships + owners can invite members
- **UPDATE**: Users can update their own memberships + owners can manage memberships
- **DELETE**: Users can leave organizations + owners can remove members

### user_settings
- **ALL**: Users can only access their own settings

### freelance_projects
- **ALL**: Users can only access projects in organizations they have access to

## Functions

### public.user_has_organization_access(org_id UUID)
**Purpose**: Checks if the current user has access to a specific organization.
**Returns**: BOOLEAN
**Logic**: Returns true if user is owner or member of the organization.

### public.get_user_default_organization()
**Purpose**: Gets the user's default organization (first owned, then first membership).
**Returns**: UUID (organization ID)
**Usage**: Used for setting initial organization context.

### public.update_updated_at_column()
**Purpose**: Trigger function to automatically update updated_at timestamps.
**Usage**: Applied to all tables with updated_at columns.

## Triggers

### Automatic Timestamp Updates
All tables with `updated_at` columns have triggers that automatically update the timestamp on row modifications:
- `update_organizations_updated_at`
- `update_organization_members_updated_at`
- `update_user_settings_updated_at`
- `update_freelance_projects_updated_at`

## Usage Patterns

### Organization Creation (Registration)
1. User registers → creates auth.users record
2. Frontend calls organization creation utility
3. Organization is created with user as owner
4. User is added to organization_members with 'owner' role

### Project Management
1. User selects organization context
2. All project operations are scoped to that organization
3. RLS policies ensure data isolation
4. Projects are automatically assigned to current organization

### Multi-Organization Support
1. Users can be members of multiple organizations
2. Organization context switching updates frontend state
3. All data queries are automatically filtered by organization
4. Complete data isolation between organizations

## Migration and Deployment

### Fresh Database Setup
```sql
-- Apply the complete schema
psql -d your_database -f supabase/migrations/001_complete_schema.sql
```

### Verification Queries
```sql
-- Check tables exist
SELECT tablename FROM pg_tables WHERE schemaname = 'public';

-- Check RLS is enabled
SELECT tablename, rowsecurity FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

-- Check policies exist
SELECT tablename, policyname FROM pg_policies 
WHERE schemaname = 'public';
```

## Security Considerations

### Data Isolation
- Complete separation between organizations
- No cross-organization data leakage possible
- RLS policies enforce access at database level

### Authentication
- All operations require authenticated user
- User context automatically applied to all queries
- No manual user ID passing required

### Authorization
- Role-based access within organizations
- Owner privileges for organization management
- Member access for project operations

### Performance
- Optimized indexes for multi-tenant queries
- Efficient RLS policy implementation
- Minimal overhead for security checks

This schema provides a robust, secure, and scalable foundation for the Freelancify multi-tenant application.
