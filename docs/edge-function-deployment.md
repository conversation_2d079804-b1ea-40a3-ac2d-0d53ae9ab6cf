# Edge Function Deployment für sichere Organisationserstellung

## Übersicht
Diese Anleitung erklärt, wie die `create-organization` Edge Function deployed wird, um eine sichere Organisationserstellung zu gewährleisten.

## Warum Edge Functions?
- **Maximale Sicherheit**: Service Role Rechte umgehen RLS Policies kontrolliert
- **Validierung**: Server-seitige Validierung der Eingaben
- **Atomare Operationen**: Organisation und Mitgliedschaft werden zusammen erstellt
- **Fehlerbehandlung**: Automatisches Cleanup bei Fehlern

## Deployment Schritte

### 1. Edge Function deployen
```bash
# Stelle sicher, dass du im Projektverzeichnis bist
cd /path/to/your/project

# Deploye die Edge Function
npx supabase functions deploy create-organization

# Oder mit Supabase CLI
supabase functions deploy create-organization
```

### 2. Environment Variablen prüfen
Die Edge Function benötigt diese Environment Variablen (automatisch verfügbar):
- `SUPABASE_URL` - Deine Supabase Projekt URL
- `SUPABASE_SERVICE_ROLE_KEY` - Service Role Key (mit RLS Bypass)
- `SUPABASE_ANON_KEY` - Anon Key für User Authentication

### 3. Sichere RLS Policies anwenden
```sql
-- Führe diese Migration aus:
-- supabase/migrations/20250712120008-secure-rls-policies.sql

-- Entfernt permissive Policies
DROP POLICY IF EXISTS "allow_organization_insert" ON public.organizations;
DROP POLICY IF EXISTS "allow_membership_insert" ON public.organization_members;

-- Erstellt sichere Policies (verbietet direkte Client-INSERTs)
CREATE POLICY "deny_direct_organization_insert" 
ON public.organizations FOR INSERT 
WITH CHECK (false);

CREATE POLICY "deny_direct_membership_insert" 
ON public.organization_members FOR INSERT 
WITH CHECK (false);
```

### 4. Funktionalität testen
```bash
# Teste die Edge Function direkt
curl -X POST 'https://your-project.supabase.co/functions/v1/create-organization' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "organizationName": "Test Organization",
    "organizationDescription": "Test Description"
  }'
```

## Sicherheitsfeatures

### 1. Authentifizierung
- Prüft gültigen JWT Token
- Verifiziert User-Authentifizierung
- Verwendet User ID aus Token

### 2. Validierung
- Organisationsname ist erforderlich
- Automatische Slug-Generierung
- Eindeutige Slug-Prüfung
- Input Sanitization

### 3. Atomare Operationen
- Organisation und Mitgliedschaft werden zusammen erstellt
- Automatisches Cleanup bei Fehlern
- Transaktionale Sicherheit

### 4. RLS Bypass
- Service Role umgeht RLS Policies kontrolliert
- Nur die Edge Function kann Organisationen erstellen
- Client-seitige Erstellung ist komplett blockiert

## Fehlerbehandlung

### Häufige Fehler und Lösungen

**"Function not found"**
```bash
# Edge Function neu deployen
npx supabase functions deploy create-organization
```

**"User not authenticated"**
- Prüfe, ob der Authorization Header korrekt gesetzt ist
- Stelle sicher, dass der User eingeloggt ist

**"Organization name is required"**
- Validiere Input im Frontend
- Stelle sicher, dass organizationName nicht leer ist

**"Failed to create organization"**
- Prüfe Supabase Logs: `npx supabase functions logs create-organization`
- Überprüfe Database Constraints

## Monitoring

### Logs anzeigen
```bash
# Aktuelle Logs anzeigen
npx supabase functions logs create-organization

# Live Logs verfolgen
npx supabase functions logs create-organization --follow
```

### Metriken überwachen
- Erfolgsrate der Organisationserstellung
- Antwortzeiten der Edge Function
- Fehlerrate und häufige Fehlertypen

## Rollback Plan

Falls Probleme auftreten:

### 1. Temporär permissive Policies aktivieren
```sql
-- Notfall: Erlaube direkte Organisationserstellung
DROP POLICY IF EXISTS "deny_direct_organization_insert" ON public.organizations;
DROP POLICY IF EXISTS "deny_direct_membership_insert" ON public.organization_members;

CREATE POLICY "temp_allow_org_insert" 
ON public.organizations FOR INSERT 
WITH CHECK (auth.uid() IS NOT NULL AND owner_id = auth.uid());

CREATE POLICY "temp_allow_membership_insert" 
ON public.organization_members FOR INSERT 
WITH CHECK (auth.uid() IS NOT NULL AND user_id = auth.uid());
```

### 2. Frontend auf direkte DB-Calls umstellen
```typescript
// Temporärer Fallback im Auth.tsx
const fallbackCreateOrganization = async () => {
  const { data, error } = await supabase
    .from('organizations')
    .insert([{
      name: organizationName,
      slug: generateSlugFromName(organizationName),
      owner_id: authData.user.id,
      description: `${organizationName} - Freelance Organization`
    }])
    .select()
    .single();
  
  if (error) throw error;
  return data;
};
```

## Produktions-Checkliste

- [ ] Edge Function erfolgreich deployed
- [ ] Sichere RLS Policies angewendet
- [ ] Registrierung getestet
- [ ] Organisationserstellung getestet
- [ ] Logs und Monitoring eingerichtet
- [ ] Rollback-Plan dokumentiert
- [ ] Team über neue Architektur informiert

## Vorteile dieser Lösung

✅ **Maximale Sicherheit** - Keine direkten Client-INSERTs möglich
✅ **Kontrollierte Erstellung** - Alle Organisationen durchlaufen Validierung
✅ **Atomare Operationen** - Organisation + Mitgliedschaft zusammen
✅ **Saubere Architektur** - Trennung von Client und Server Logic
✅ **Skalierbar** - Edge Functions skalieren automatisch
✅ **Auditierbar** - Alle Organisationserstellungen sind nachverfolgbar
