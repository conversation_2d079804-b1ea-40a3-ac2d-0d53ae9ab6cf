# Database Migration Sequence for Multi-Tenancy

## Overview
This document outlines the correct sequence for applying database migrations to implement multi-tenancy in Freelancify.

## Migration Files (Apply in this exact order)

### 1. **20250712120000-multi-tenancy-schema.sql**
**Purpose**: Creates the foundation for multi-tenancy
- Creates `organizations` table
- Creates `organization_members` table  
- Adds `organization_id` column to `freelance_projects`
- Sets up indexes and basic structure
- Creates helper functions and triggers

### 2. **20250712120001-migrate-existing-data.sql**
**Purpose**: Migrates existing user data to the new structure
- Creates default organizations for existing users
- Assigns existing projects to user organizations
- Ensures no data loss during migration

### 3. **20250712120002-update-rls-policies.sql**
**Purpose**: Initial RLS policies (contains recursive issues - will be fixed)
- Updates RLS policies for organization-based access
- Creates helper functions
- **Note**: This migration contains recursive policies that cause issues

### 4. **20250712120004-reset-rls-policies.sql** ⚠️ **IMPORTANT**
**Purpose**: Fixes the recursive RLS policy issues
- Disables <PERSON><PERSON> temporarily
- Removes all problematic policies
- Cleans up the policy mess

### 5. **20250712120005-create-simple-rls-policies.sql** ✅ **FINAL**
**Purpose**: Creates clean, working RLS policies
- Re-enables RLS
- Creates simple, non-recursive policies
- Provides secure multi-tenant data isolation

## Quick Application Commands

If you're setting up a fresh database:

```bash
# Apply all migrations in sequence
psql -d your_database -f supabase/migrations/20250712120000-multi-tenancy-schema.sql
psql -d your_database -f supabase/migrations/20250712120001-migrate-existing-data.sql
psql -d your_database -f supabase/migrations/20250712120004-reset-rls-policies.sql
psql -d your_database -f supabase/migrations/20250712120005-create-simple-rls-policies.sql
```

**Note**: Skip migration `20250712120002-update-rls-policies.sql` as it contains the problematic recursive policies.

## Verification Queries

After applying all migrations, verify the setup:

```sql
-- Check organizations exist
SELECT COUNT(*) FROM public.organizations;

-- Check organization members exist  
SELECT COUNT(*) FROM public.organization_members;

-- Check projects have organization_id
SELECT COUNT(*) FROM public.freelance_projects WHERE organization_id IS NOT NULL;

-- Check RLS policies are active
SELECT tablename, policyname FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('organizations', 'organization_members', 'freelance_projects');
```

## Rollback Plan

If you need to rollback:

```sql
-- Remove organization_id from projects
ALTER TABLE public.freelance_projects DROP COLUMN IF EXISTS organization_id;

-- Drop new tables
DROP TABLE IF EXISTS public.organization_members;
DROP TABLE IF EXISTS public.organizations;

-- Restore original RLS policies for freelance_projects
-- (You'll need to recreate the original user-based policies)
```

## Success Indicators

✅ All migrations applied without errors
✅ Existing users can log in and see their organizations
✅ Projects are visible and organization-scoped
✅ New users can register and create organizations
✅ No infinite recursion errors in browser console
✅ Organization switching works in the UI

## Troubleshooting

### Issue: "infinite recursion detected in policy"
**Solution**: Apply migrations 4 and 5 to fix RLS policies

### Issue: "No organization selected"
**Solution**: Check that migration 1 created organizations for existing users

### Issue: Projects not visible
**Solution**: Verify migration 1 assigned organization_id to existing projects

### Issue: New users can't register
**Solution**: Check that the organization creation trigger is working
