-- Restore secure RLS policies for organization creation
-- This migration removes the permissive policies and creates secure ones
-- Organization creation will be handled by Edge Functions with service role

-- Remove the permissive INSERT policies
DROP POLICY IF EXISTS "allow_organization_insert" ON public.organizations;
DROP POLICY IF EXISTS "allow_membership_insert" ON public.organization_members;

-- Create secure INSERT policies that prevent direct client-side creation
-- These policies will deny all direct INSERTs from the client
-- Organization creation must go through the Edge Function

-- Organizations: No direct INSERT allowed from client
CREATE POLICY "deny_direct_organization_insert" 
ON public.organizations FOR INSERT 
WITH CHECK (false);

-- Organization Members: No direct INSERT allowed from client  
CREATE POLICY "deny_direct_membership_insert" 
ON public.organization_members FOR INSERT 
WITH CHECK (false);

-- Note: The Edge Function uses the service role key which bypasses RLS entirely
-- This ensures that only the controlled Edge Function can create organizations
-- while maintaining maximum security for direct client access

-- Keep all other policies secure (SELECT, UPDATE, DELETE remain as before)
-- These ensure proper data isolation after creation
