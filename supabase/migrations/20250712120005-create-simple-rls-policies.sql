-- Create simple, non-recursive RLS policies
-- This migration creates clean RLS policies that avoid infinite recursion

-- Re-enable RLS for all tables
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.freelance_projects ENABLE ROW LEVEL SECURITY;

-- ========================================
-- ORGANIZATIONS TABLE POLICIES
-- ========================================

-- Organization owners can view their own organizations
CREATE POLICY "owners_can_view_own_orgs" 
ON public.organizations FOR SELECT 
USING (owner_id = auth.uid());

-- Organization owners can update their own organizations
CREATE POLICY "owners_can_update_own_orgs" 
ON public.organizations FOR UPDATE 
USING (owner_id = auth.uid());

-- Authenticated users can create organizations (they become the owner)
CREATE POLICY "users_can_create_orgs" 
ON public.organizations FOR INSERT 
WITH CHECK (owner_id = auth.uid());

-- Organization owners can delete their own organizations
CREATE POLICY "owners_can_delete_own_orgs" 
ON public.organizations FOR DELETE 
USING (owner_id = auth.uid());

-- ========================================
-- ORGANIZATION_MEMBERS TABLE POLICIES
-- ========================================

-- Users can view their own memberships
CREATE POLICY "users_can_view_own_memberships" 
ON public.organization_members FOR SELECT 
USING (user_id = auth.uid());

-- Users can insert their own memberships (when joining organizations)
CREATE POLICY "users_can_insert_own_memberships" 
ON public.organization_members FOR INSERT 
WITH CHECK (user_id = auth.uid());

-- Users can update their own memberships (for role changes they accept)
CREATE POLICY "users_can_update_own_memberships" 
ON public.organization_members FOR UPDATE 
USING (user_id = auth.uid());

-- Users can delete their own memberships (leave organizations)
CREATE POLICY "users_can_delete_own_memberships" 
ON public.organization_members FOR DELETE 
USING (user_id = auth.uid());

-- Organization owners can view all memberships in their organizations
CREATE POLICY "owners_can_view_org_memberships" 
ON public.organization_members FOR SELECT 
USING (
  organization_id IN (
    SELECT id FROM public.organizations WHERE owner_id = auth.uid()
  )
);

-- Organization owners can manage memberships in their organizations
CREATE POLICY "owners_can_manage_org_memberships" 
ON public.organization_members FOR ALL 
USING (
  organization_id IN (
    SELECT id FROM public.organizations WHERE owner_id = auth.uid()
  )
);

-- ========================================
-- FREELANCE_PROJECTS TABLE POLICIES
-- ========================================

-- Users can view projects in organizations they own
CREATE POLICY "users_can_view_own_org_projects" 
ON public.freelance_projects FOR SELECT 
USING (
  organization_id IN (
    SELECT id FROM public.organizations WHERE owner_id = auth.uid()
  )
);

-- Users can create projects in organizations they own
CREATE POLICY "users_can_create_own_org_projects" 
ON public.freelance_projects FOR INSERT 
WITH CHECK (
  organization_id IN (
    SELECT id FROM public.organizations WHERE owner_id = auth.uid()
  )
);

-- Users can update projects in organizations they own
CREATE POLICY "users_can_update_own_org_projects" 
ON public.freelance_projects FOR UPDATE 
USING (
  organization_id IN (
    SELECT id FROM public.organizations WHERE owner_id = auth.uid()
  )
);

-- Users can delete projects in organizations they own
CREATE POLICY "users_can_delete_own_org_projects" 
ON public.freelance_projects FOR DELETE 
USING (
  organization_id IN (
    SELECT id FROM public.organizations WHERE owner_id = auth.uid()
  )
);

-- ========================================
-- COMMENTS AND NOTES
-- ========================================

-- These policies are designed to be simple and avoid recursion:
-- 1. Organizations: Only owners can access their own organizations
-- 2. Organization Members: Users can manage their own memberships + owners can manage all
-- 3. Projects: Only accessible through organizations the user owns
-- 
-- Future enhancements can add more complex role-based access (admin, member roles)
-- but this foundation provides secure multi-tenant data isolation.
