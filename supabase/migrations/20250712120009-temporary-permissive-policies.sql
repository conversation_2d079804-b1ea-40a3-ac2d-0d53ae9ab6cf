-- Temporary permissive policies for organization creation
-- Use this migration if the Edge Function is not yet deployed
-- This allows the fallback method to work while maintaining some security

-- Remove the restrictive policies temporarily
DROP POLICY IF EXISTS "deny_direct_organization_insert" ON public.organizations;
DROP POLICY IF EXISTS "deny_direct_membership_insert" ON public.organization_members;

-- Create temporary permissive policies for authenticated users
CREATE POLICY "temp_authenticated_org_insert" 
ON public.organizations FOR INSERT 
WITH CHECK (
  auth.uid() IS NOT NULL 
  AND owner_id = auth.uid()
);

CREATE POLICY "temp_authenticated_membership_insert" 
ON public.organization_members FOR INSERT 
WITH CHECK (
  auth.uid() IS NOT NULL 
  AND user_id = auth.uid()
);

-- Note: These policies are more permissive than the secure Edge Function approach
-- but still require authentication and proper ownership
-- 
-- Once the Edge Function is deployed and tested, you should apply:
-- supabase/migrations/20250712120008-secure-rls-policies.sql
-- 
-- to restore the most secure configuration
