-- Migration script to handle existing users and their data
-- This script creates organizations for existing users and assigns their projects

-- Create organizations for existing users who don't have one yet
DO $$
DECLARE
  user_record RECORD;
  org_id UUID;
  org_name TEXT;
  org_slug TEXT;
  counter INTEGER := 0;
BEGIN
  -- Loop through all existing users who don't have an organization yet
  FOR user_record IN 
    SELECT DISTINCT u.id, u.email 
    FROM auth.users u
    LEFT JOIN public.organization_members om ON u.id = om.user_id
    WHERE om.user_id IS NULL
  LOOP
    -- Create organization name and slug from email
    org_name := split_part(user_record.email, '@', 1) || '''s Organization';
    org_slug := lower(replace(split_part(user_record.email, '@', 1), '.', '-')) || '-org';
    
    -- Ensure unique slug
    WHILE EXISTS (SELECT 1 FROM public.organizations WHERE slug = org_slug) LOOP
      counter := counter + 1;
      org_slug := lower(replace(split_part(user_record.email, '@', 1), '.', '-')) || '-org-' || counter::text;
    END LOOP;
    
    -- Create organization
    INSERT INTO public.organizations (name, slug, owner_id, description)
    VALUES (org_name, org_slug, user_record.id, 'Default organization for ' || user_record.email)
    RETURNING id INTO org_id;
    
    -- Add user as owner of the organization
    INSERT INTO public.organization_members (organization_id, user_id, role)
    VALUES (org_id, user_record.id, 'owner');
    
    -- Assign all existing projects of this user to their new organization
    UPDATE public.freelance_projects 
    SET organization_id = org_id 
    WHERE user_id = user_record.id AND organization_id IS NULL;
    
    RAISE NOTICE 'Created organization % for user %', org_name, user_record.email;
  END LOOP;
END $$;

-- Verify migration results
DO $$
DECLARE
  total_users INTEGER;
  users_with_orgs INTEGER;
  projects_without_org INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_users FROM auth.users;
  SELECT COUNT(DISTINCT user_id) INTO users_with_orgs FROM public.organization_members;
  SELECT COUNT(*) INTO projects_without_org FROM public.freelance_projects WHERE organization_id IS NULL;
  
  RAISE NOTICE 'Migration Summary:';
  RAISE NOTICE '- Total users: %', total_users;
  RAISE NOTICE '- Users with organizations: %', users_with_orgs;
  RAISE NOTICE '- Projects without organization: %', projects_without_org;
  
  IF projects_without_org > 0 THEN
    RAISE WARNING 'Some projects still don''t have an organization assigned!';
  END IF;
END $$;
