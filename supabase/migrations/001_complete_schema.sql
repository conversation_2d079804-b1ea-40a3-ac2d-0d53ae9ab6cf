-- ============================================================================
-- FREELANCIFY - COMPLETE DATABASE SCHEMA
-- ============================================================================
-- This file contains the complete database schema for the Freelancify application
-- including multi-tenant organization-based data isolation, user management,
-- project management, and all necessary security policies.
--
-- Features:
-- - Multi-tenant architecture with organization-based data isolation
-- - Row Level Security (RLS) for complete data separation
-- - User authentication and authorization
-- - Project management with organization scoping
-- - Automatic organization creation during user registration
-- - Performance optimized with proper indexes
--
-- Safe to run multiple times (idempotent)
-- ============================================================================

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- Organizations table - Core entity for multi-tenancy
-- Each organization represents a separate tenant with isolated data
CREATE TABLE IF NOT EXISTS public.organizations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL CHECK (length(trim(name)) > 0),
  slug TEXT UNIQUE NOT NULL CHECK (length(trim(slug)) > 0),
  description TEXT,
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Organization members table - Manages user-organization relationships
-- Supports role-based access control (owner, admin, member)
CREATE TABLE IF NOT EXISTS public.organization_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member')) DEFAULT 'member',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(organization_id, user_id)
);

-- User settings table - Stores user-specific preferences and profile data
-- This data is user-specific, not organization-specific
CREATE TABLE IF NOT EXISTS public.user_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  timezone TEXT DEFAULT 'UTC',
  language TEXT DEFAULT 'de',
  email_notifications BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Freelance projects table - Core business entity
-- Each project belongs to exactly one organization for data isolation
CREATE TABLE IF NOT EXISTS public.freelance_projects (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL CHECK (length(trim(title)) > 0),
  description TEXT,
  client_name TEXT,
  client_email TEXT,
  client_phone TEXT,
  project_url TEXT,
  budget DECIMAL(10,2),
  currency TEXT DEFAULT 'EUR',
  status TEXT NOT NULL CHECK (status IN ('draft', 'active', 'completed', 'cancelled')) DEFAULT 'draft',
  priority TEXT NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
  start_date DATE,
  end_date DATE,
  deadline DATE,
  tags TEXT[],
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Ensure end_date is after start_date
  CONSTRAINT valid_date_range CHECK (end_date IS NULL OR start_date IS NULL OR end_date >= start_date),
  -- Ensure deadline is in the future when set
  CONSTRAINT valid_deadline CHECK (deadline IS NULL OR deadline >= CURRENT_DATE),
  -- Ensure budget is positive
  CONSTRAINT positive_budget CHECK (budget IS NULL OR budget > 0)
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Organizations indexes
CREATE INDEX IF NOT EXISTS idx_organizations_owner_id ON public.organizations(owner_id);
CREATE INDEX IF NOT EXISTS idx_organizations_slug ON public.organizations(slug);
CREATE INDEX IF NOT EXISTS idx_organizations_created_at ON public.organizations(created_at DESC);

-- Organization members indexes
CREATE INDEX IF NOT EXISTS idx_organization_members_org_id ON public.organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON public.organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_role ON public.organization_members(role);

-- User settings indexes
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON public.user_settings(user_id);

-- Freelance projects indexes
CREATE INDEX IF NOT EXISTS idx_freelance_projects_org_id ON public.freelance_projects(organization_id);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_user_id ON public.freelance_projects(user_id);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_status ON public.freelance_projects(status);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_priority ON public.freelance_projects(priority);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_created_at ON public.freelance_projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_deadline ON public.freelance_projects(deadline) WHERE deadline IS NOT NULL;

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_freelance_projects_org_status ON public.freelance_projects(organization_id, status);
CREATE INDEX IF NOT EXISTS idx_freelance_projects_org_user ON public.freelance_projects(organization_id, user_id);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Organizations updated_at trigger
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.triggers 
    WHERE trigger_name = 'update_organizations_updated_at'
    AND event_object_table = 'organizations'
  ) THEN
    CREATE TRIGGER update_organizations_updated_at
      BEFORE UPDATE ON public.organizations
      FOR EACH ROW
      EXECUTE FUNCTION public.update_updated_at_column();
  END IF;
END $$;

-- Organization members updated_at trigger
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.triggers 
    WHERE trigger_name = 'update_organization_members_updated_at'
    AND event_object_table = 'organization_members'
  ) THEN
    CREATE TRIGGER update_organization_members_updated_at
      BEFORE UPDATE ON public.organization_members
      FOR EACH ROW
      EXECUTE FUNCTION public.update_updated_at_column();
  END IF;
END $$;

-- User settings updated_at trigger
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.triggers 
    WHERE trigger_name = 'update_user_settings_updated_at'
    AND event_object_table = 'user_settings'
  ) THEN
    CREATE TRIGGER update_user_settings_updated_at
      BEFORE UPDATE ON public.user_settings
      FOR EACH ROW
      EXECUTE FUNCTION public.update_updated_at_column();
  END IF;
END $$;

-- Freelance projects updated_at trigger
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.triggers 
    WHERE trigger_name = 'update_freelance_projects_updated_at'
    AND event_object_table = 'freelance_projects'
  ) THEN
    CREATE TRIGGER update_freelance_projects_updated_at
      BEFORE UPDATE ON public.freelance_projects
      FOR EACH ROW
      EXECUTE FUNCTION public.update_updated_at_column();
  END IF;
END $$;

-- ============================================================================
-- ROW LEVEL SECURITY (RLS) SETUP
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.freelance_projects ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- SECURITY HELPER FUNCTIONS
-- ============================================================================

-- Function to check if user has access to an organization
-- Returns true if user is owner or member of the organization
CREATE OR REPLACE FUNCTION public.user_has_organization_access(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is authenticated
  IF auth.uid() IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Check if user is owner or member of the organization
  RETURN EXISTS (
    SELECT 1 FROM public.organizations o
    LEFT JOIN public.organization_members om ON o.id = om.organization_id
    WHERE o.id = org_id
    AND (
      o.owner_id = auth.uid()
      OR (om.user_id = auth.uid() AND om.organization_id = org_id)
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's default organization (first one they own or are member of)
CREATE OR REPLACE FUNCTION public.get_user_default_organization()
RETURNS UUID AS $$
DECLARE
  org_id UUID;
BEGIN
  -- First try to find an organization the user owns
  SELECT id INTO org_id
  FROM public.organizations
  WHERE owner_id = auth.uid()
  ORDER BY created_at ASC
  LIMIT 1;

  -- If no owned organization, find one they're a member of
  IF org_id IS NULL THEN
    SELECT organization_id INTO org_id
    FROM public.organization_members
    WHERE user_id = auth.uid()
    ORDER BY created_at ASC
    LIMIT 1;
  END IF;

  RETURN org_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- RLS POLICIES - ORGANIZATIONS
-- ============================================================================

-- Users can view organizations they own or are members of
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'organizations'
    AND policyname = 'organizations_select_policy'
  ) THEN
    CREATE POLICY "organizations_select_policy"
    ON public.organizations FOR SELECT
    USING (
      auth.uid() IS NOT NULL
      AND (
        owner_id = auth.uid()
        OR id IN (
          SELECT organization_id
          FROM public.organization_members
          WHERE user_id = auth.uid()
        )
      )
    );
  END IF;
END $$;

-- Users can create organizations (they become the owner)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'organizations'
    AND policyname = 'organizations_insert_policy'
  ) THEN
    CREATE POLICY "organizations_insert_policy"
    ON public.organizations FOR INSERT
    WITH CHECK (
      auth.uid() IS NOT NULL
      AND owner_id = auth.uid()
    );
  END IF;
END $$;

-- Organization owners can update their organizations
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'organizations'
    AND policyname = 'organizations_update_policy'
  ) THEN
    CREATE POLICY "organizations_update_policy"
    ON public.organizations FOR UPDATE
    USING (
      auth.uid() IS NOT NULL
      AND owner_id = auth.uid()
    );
  END IF;
END $$;

-- Organization owners can delete their organizations
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'organizations'
    AND policyname = 'organizations_delete_policy'
  ) THEN
    CREATE POLICY "organizations_delete_policy"
    ON public.organizations FOR DELETE
    USING (
      auth.uid() IS NOT NULL
      AND owner_id = auth.uid()
    );
  END IF;
END $$;

-- ============================================================================
-- RLS POLICIES - ORGANIZATION MEMBERS
-- ============================================================================

-- Users can view their own memberships and memberships in organizations they own
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'organization_members'
    AND policyname = 'organization_members_select_policy'
  ) THEN
    CREATE POLICY "organization_members_select_policy"
    ON public.organization_members FOR SELECT
    USING (
      auth.uid() IS NOT NULL
      AND (
        user_id = auth.uid()
        OR organization_id IN (
          SELECT id FROM public.organizations WHERE owner_id = auth.uid()
        )
      )
    );
  END IF;
END $$;

-- Users can create their own memberships (when invited)
-- Organization owners can create memberships for their organizations
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'organization_members'
    AND policyname = 'organization_members_insert_policy'
  ) THEN
    CREATE POLICY "organization_members_insert_policy"
    ON public.organization_members FOR INSERT
    WITH CHECK (
      auth.uid() IS NOT NULL
      AND (
        user_id = auth.uid()
        OR organization_id IN (
          SELECT id FROM public.organizations WHERE owner_id = auth.uid()
        )
      )
    );
  END IF;
END $$;

-- Users can update their own memberships
-- Organization owners can update memberships in their organizations
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'organization_members'
    AND policyname = 'organization_members_update_policy'
  ) THEN
    CREATE POLICY "organization_members_update_policy"
    ON public.organization_members FOR UPDATE
    USING (
      auth.uid() IS NOT NULL
      AND (
        user_id = auth.uid()
        OR organization_id IN (
          SELECT id FROM public.organizations WHERE owner_id = auth.uid()
        )
      )
    );
  END IF;
END $$;

-- Users can delete their own memberships (leave organization)
-- Organization owners can remove members from their organizations
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'organization_members'
    AND policyname = 'organization_members_delete_policy'
  ) THEN
    CREATE POLICY "organization_members_delete_policy"
    ON public.organization_members FOR DELETE
    USING (
      auth.uid() IS NOT NULL
      AND (
        user_id = auth.uid()
        OR organization_id IN (
          SELECT id FROM public.organizations WHERE owner_id = auth.uid()
        )
      )
    );
  END IF;
END $$;

-- ============================================================================
-- RLS POLICIES - USER SETTINGS
-- ============================================================================

-- Users can only access their own settings
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'user_settings'
    AND policyname = 'user_settings_select_policy'
  ) THEN
    CREATE POLICY "user_settings_select_policy"
    ON public.user_settings FOR SELECT
    USING (auth.uid() IS NOT NULL AND user_id = auth.uid());
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'user_settings'
    AND policyname = 'user_settings_insert_policy'
  ) THEN
    CREATE POLICY "user_settings_insert_policy"
    ON public.user_settings FOR INSERT
    WITH CHECK (auth.uid() IS NOT NULL AND user_id = auth.uid());
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'user_settings'
    AND policyname = 'user_settings_update_policy'
  ) THEN
    CREATE POLICY "user_settings_update_policy"
    ON public.user_settings FOR UPDATE
    USING (auth.uid() IS NOT NULL AND user_id = auth.uid());
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'user_settings'
    AND policyname = 'user_settings_delete_policy'
  ) THEN
    CREATE POLICY "user_settings_delete_policy"
    ON public.user_settings FOR DELETE
    USING (auth.uid() IS NOT NULL AND user_id = auth.uid());
  END IF;
END $$;

-- ============================================================================
-- RLS POLICIES - FREELANCE PROJECTS
-- ============================================================================

-- Users can view projects in organizations they have access to
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'freelance_projects'
    AND policyname = 'freelance_projects_select_policy'
  ) THEN
    CREATE POLICY "freelance_projects_select_policy"
    ON public.freelance_projects FOR SELECT
    USING (
      auth.uid() IS NOT NULL
      AND organization_id IS NOT NULL
      AND public.user_has_organization_access(organization_id)
    );
  END IF;
END $$;

-- Users can create projects in organizations they have access to
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'freelance_projects'
    AND policyname = 'freelance_projects_insert_policy'
  ) THEN
    CREATE POLICY "freelance_projects_insert_policy"
    ON public.freelance_projects FOR INSERT
    WITH CHECK (
      auth.uid() IS NOT NULL
      AND organization_id IS NOT NULL
      AND public.user_has_organization_access(organization_id)
    );
  END IF;
END $$;

-- Users can update projects in organizations they have access to
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'freelance_projects'
    AND policyname = 'freelance_projects_update_policy'
  ) THEN
    CREATE POLICY "freelance_projects_update_policy"
    ON public.freelance_projects FOR UPDATE
    USING (
      auth.uid() IS NOT NULL
      AND organization_id IS NOT NULL
      AND public.user_has_organization_access(organization_id)
    );
  END IF;
END $$;

-- Users can delete projects in organizations they have access to
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename = 'freelance_projects'
    AND policyname = 'freelance_projects_delete_policy'
  ) THEN
    CREATE POLICY "freelance_projects_delete_policy"
    ON public.freelance_projects FOR DELETE
    USING (
      auth.uid() IS NOT NULL
      AND organization_id IS NOT NULL
      AND public.user_has_organization_access(organization_id)
    );
  END IF;
END $$;

-- ============================================================================
-- GRANTS AND PERMISSIONS
-- ============================================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Grant permissions on tables
GRANT ALL ON public.organizations TO authenticated;
GRANT ALL ON public.organization_members TO authenticated;
GRANT ALL ON public.user_settings TO authenticated;
GRANT ALL ON public.freelance_projects TO authenticated;

-- Grant permissions on sequences
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.update_updated_at_column() TO authenticated;
GRANT EXECUTE ON FUNCTION public.user_has_organization_access(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_default_organization() TO authenticated;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'FREELANCIFY DATABASE SCHEMA SETUP COMPLETE';
  RAISE NOTICE '============================================================================';
  RAISE NOTICE 'Tables created:';
  RAISE NOTICE '- organizations (multi-tenant core entity)';
  RAISE NOTICE '- organization_members (user-organization relationships)';
  RAISE NOTICE '- user_settings (user preferences and profile data)';
  RAISE NOTICE '- freelance_projects (core business entity with organization isolation)';
  RAISE NOTICE '';
  RAISE NOTICE 'Security features enabled:';
  RAISE NOTICE '- Row Level Security (RLS) on all tables';
  RAISE NOTICE '- Multi-tenant data isolation';
  RAISE NOTICE '- Organization-based access control';
  RAISE NOTICE '- User-specific settings isolation';
  RAISE NOTICE '';
  RAISE NOTICE 'Performance optimizations:';
  RAISE NOTICE '- Comprehensive indexing strategy';
  RAISE NOTICE '- Composite indexes for common queries';
  RAISE NOTICE '- Automatic timestamp updates';
  RAISE NOTICE '';
  RAISE NOTICE 'Ready for production use!';
  RAISE NOTICE '============================================================================';
END $$;
