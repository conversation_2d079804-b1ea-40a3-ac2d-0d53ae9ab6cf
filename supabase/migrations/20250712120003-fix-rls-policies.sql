-- Fix RLS policies to prevent infinite recursion
-- This migration fixes the organization_members RLS policies that were causing infinite recursion

-- Drop the problematic policies
DROP POLICY IF EXISTS "Users can view memberships for their organizations" ON public.organization_members;
DROP POLICY IF EXISTS "Organization owners and admins can manage memberships" ON public.organization_members;

-- Create simpler, non-recursive policies for organization_members
-- Users can view their own memberships
CREATE POLICY "Users can view their own memberships" 
ON public.organization_members FOR SELECT 
USING (user_id = auth.uid());

-- Users can view memberships in organizations they own
CREATE POLICY "Organization owners can view all memberships" 
ON public.organization_members FOR SELECT 
USING (
  organization_id IN (
    SELECT id 
    FROM public.organizations 
    WHERE owner_id = auth.uid()
  )
);

-- Organization owners can manage memberships
CREATE POLICY "Organization owners can manage memberships" 
ON public.organization_members FOR ALL 
USING (
  organization_id IN (
    SELECT id 
    FROM public.organizations 
    WHERE owner_id = auth.uid()
  )
);

-- Users can insert their own membership when invited
CREATE POLICY "Users can insert their own membership" 
ON public.organization_members FOR INSERT 
WITH CHECK (user_id = auth.uid());

-- Users can update their own membership (for accepting invitations)
CREATE POLICY "Users can update their own membership" 
ON public.organization_members FOR UPDATE 
USING (user_id = auth.uid());

-- Users can delete their own membership (leave organization)
CREATE POLICY "Users can delete their own membership" 
ON public.organization_members FOR DELETE 
USING (user_id = auth.uid());
