-- Temporarily disable <PERSON><PERSON> for organization creation during registration
-- This is a workaround for the authentication timing issue during user registration

-- Remove all INSERT policies that are causing issues
DROP POLICY IF EXISTS "users_can_create_orgs" ON public.organizations;
DROP POLICY IF EXISTS "authenticated_users_can_create_orgs" ON public.organizations;
DROP POLICY IF EXISTS "allow_org_creation" ON public.organizations;

DROP POLICY IF EXISTS "users_can_insert_own_memberships" ON public.organization_members;
DROP POLICY IF EXISTS "authenticated_users_can_create_memberships" ON public.organization_members;
DROP POLICY IF EXISTS "allow_membership_creation" ON public.organization_members;

-- Create very permissive INSERT policies (security handled in application)
CREATE POLICY "allow_organization_insert" 
ON public.organizations FOR INSERT 
WITH CHECK (true);

CREATE POLICY "allow_membership_insert" 
ON public.organization_members FOR INSERT 
WITH CHECK (true);

-- Keep the SELECT, UPDATE, DELETE policies secure
-- These ensure data isolation after creation
