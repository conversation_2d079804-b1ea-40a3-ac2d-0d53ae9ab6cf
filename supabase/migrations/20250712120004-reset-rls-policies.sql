-- Reset all RLS policies to fix infinite recursion issues
-- This migration removes all problematic RLS policies and disables <PERSON><PERSON> temporarily

-- Disable RLS for all affected tables
ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.freelance_projects DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies for organizations table
DROP POLICY IF EXISTS "Users can view organizations they belong to" ON public.organizations;
DROP POLICY IF EXISTS "Organization owners can update their organizations" ON public.organizations;
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON public.organizations;
DROP POLICY IF EXISTS "Organization owners can delete their organizations" ON public.organizations;

-- Drop all existing policies for organization_members table
DROP POLICY IF EXISTS "Users can view memberships for their organizations" ON public.organization_members;
DROP POLICY IF EXISTS "Organization owners and admins can manage memberships" ON public.organization_members;
DROP POLICY IF EXISTS "Users can view their own memberships" ON public.organization_members;
DROP POLICY IF EXISTS "Organization owners can view all memberships" ON public.organization_members;
DROP POLICY IF EXISTS "Organization owners can manage memberships" ON public.organization_members;
DROP POLICY IF EXISTS "Users can insert their own membership" ON public.organization_members;
DROP POLICY IF EXISTS "Users can update their own membership" ON public.organization_members;
DROP POLICY IF EXISTS "Users can delete their own membership" ON public.organization_members;
DROP POLICY IF EXISTS "Users can insert their own membership when invited" ON public.organization_members;

-- Drop all existing policies for freelance_projects table
DROP POLICY IF EXISTS "Users can view organization projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can create projects in their organizations" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can update projects in their organizations" ON public.freelance_projects;
DROP POLICY IF EXISTS "Organization owners and admins can delete projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can view their own projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can create their own projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can update their own projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can delete their own projects" ON public.freelance_projects;
