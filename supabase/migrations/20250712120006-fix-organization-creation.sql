-- Fix organization creation during user registration
-- This migration fixes the RLS policy that prevents new organization creation

-- Drop the problematic INSERT policy for organizations
DROP POLICY IF EXISTS "users_can_create_orgs" ON public.organizations;

-- Create a more permissive policy for organization creation
-- This allows authenticated users to create organizations where they are the owner
CREATE POLICY "authenticated_users_can_create_orgs" 
ON public.organizations FOR INSERT 
WITH CHECK (
  auth.uid() IS NOT NULL 
  AND owner_id = auth.uid()
);

-- Alternative: If the above still doesn't work, we can temporarily disable R<PERSON> for INSERT
-- and handle the security in the application layer
-- Uncomment the lines below if needed:

-- DROP POLICY IF EXISTS "authenticated_users_can_create_orgs" ON public.organizations;
-- 
-- CREATE POLICY "allow_org_creation" 
-- ON public.organizations FOR INSERT 
-- WITH CHECK (auth.uid() IS NOT NULL);

-- Also ensure the organization_members INSERT policy is permissive enough
DROP POLICY IF EXISTS "users_can_insert_own_memberships" ON public.organization_members;

CREATE POLICY "authenticated_users_can_create_memberships" 
ON public.organization_members FOR INSERT 
WITH CHECK (
  auth.uid() IS NOT NULL 
  AND user_id = auth.uid()
);
