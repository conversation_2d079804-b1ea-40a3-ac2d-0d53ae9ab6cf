-- Update RLS policies for organization-based access control
-- This migration updates existing policies to work with multi-tenancy

-- Drop existing RLS policies for freelance_projects
DROP POLICY IF EXISTS "Users can view their own projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can create their own projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can update their own projects" ON public.freelance_projects;
DROP POLICY IF EXISTS "Users can delete their own projects" ON public.freelance_projects;

-- Create new organization-based RLS policies for freelance_projects
CREATE POLICY "Users can view organization projects" 
ON public.freelance_projects FOR SELECT 
USING (
  organization_id IN (
    SELECT organization_id 
    FROM public.organization_members 
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Users can create projects in their organizations" 
ON public.freelance_projects FOR INSERT 
WITH CHECK (
  organization_id IN (
    SELECT organization_id 
    FROM public.organization_members 
    WHERE user_id = auth.uid()
  )
  AND auth.uid() IS NOT NULL
);

CREATE POLICY "Users can update projects in their organizations" 
ON public.freelance_projects FOR UPDATE 
USING (
  organization_id IN (
    SELECT organization_id 
    FROM public.organization_members 
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Organization owners and admins can delete projects" 
ON public.freelance_projects FOR DELETE 
USING (
  organization_id IN (
    SELECT organization_id 
    FROM public.organization_members 
    WHERE user_id = auth.uid() 
    AND role IN ('owner', 'admin')
  )
);

-- Keep user_settings user-specific for now (personal settings)
-- The existing RLS policies for user_settings remain unchanged
-- Users can still only access their own settings

-- Create helper function to check organization membership
CREATE OR REPLACE FUNCTION public.user_has_organization_access(org_id UUID, required_role TEXT DEFAULT 'member')
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.organization_members 
    WHERE organization_id = org_id 
    AND user_id = auth.uid()
    AND (
      required_role = 'member' OR
      (required_role = 'admin' AND role IN ('admin', 'owner')) OR
      (required_role = 'owner' AND role = 'owner')
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get user's current organization (first one they belong to)
CREATE OR REPLACE FUNCTION public.get_user_default_organization()
RETURNS UUID AS $$
DECLARE
  org_id UUID;
BEGIN
  SELECT organization_id INTO org_id
  FROM public.organization_members 
  WHERE user_id = auth.uid()
  ORDER BY 
    CASE role 
      WHEN 'owner' THEN 1 
      WHEN 'admin' THEN 2 
      ELSE 3 
    END,
    created_at ASC
  LIMIT 1;
  
  RETURN org_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get all organizations for a user
CREATE OR REPLACE FUNCTION public.get_user_organizations()
RETURNS TABLE (
  organization_id UUID,
  organization_name TEXT,
  organization_slug TEXT,
  user_role TEXT,
  is_owner BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    o.id,
    o.name,
    o.slug,
    om.role,
    (om.role = 'owner') as is_owner
  FROM public.organizations o
  JOIN public.organization_members om ON o.id = om.organization_id
  WHERE om.user_id = auth.uid()
  ORDER BY 
    CASE om.role 
      WHEN 'owner' THEN 1 
      WHEN 'admin' THEN 2 
      ELSE 3 
    END,
    o.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
