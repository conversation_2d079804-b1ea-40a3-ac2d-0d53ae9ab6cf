import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role key (bypasses RLS)
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Create regular client to verify user authentication
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header provided')
    }

    // Extract token from header
    const token = authHeader.replace('Bearer ', '')
    if (!token) {
      throw new Error('Invalid authorization header format')
    }

    // Verify the user is authenticated
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)

    if (authError) {
      console.error('Auth error:', authError)
      throw new Error(`Authentication failed: ${authError.message}`)
    }

    if (!user) {
      throw new Error('No user found for provided token')
    }

    console.log('Authenticated user:', user.id, user.email)

    // Parse request body
    const { organizationName, organizationDescription } = await req.json()

    if (!organizationName || organizationName.trim().length === 0) {
      throw new Error('Organization name is required')
    }

    // Generate slug from organization name
    const generateSlug = (name: string): string => {
      return name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
        .substring(0, 50)
    }

    let orgSlug = generateSlug(organizationName)

    // Ensure unique slug
    let slugCounter = 0
    let finalSlug = orgSlug
    
    while (true) {
      const { data: existingOrg } = await supabaseAdmin
        .from('organizations')
        .select('id')
        .eq('slug', finalSlug)
        .single()

      if (!existingOrg) break

      slugCounter++
      finalSlug = `${orgSlug}-${slugCounter}`
    }

    // Create organization using service role (bypasses RLS)
    const { data: orgData, error: orgError } = await supabaseAdmin
      .from('organizations')
      .insert([{
        name: organizationName.trim(),
        slug: finalSlug,
        owner_id: user.id,
        description: organizationDescription?.trim() || `${organizationName} - Freelance Organization`
      }])
      .select()
      .single()

    if (orgError) {
      console.error('Error creating organization:', orgError)
      throw new Error('Failed to create organization')
    }

    // Add user as owner of the organization
    const { error: memberError } = await supabaseAdmin
      .from('organization_members')
      .insert([{
        organization_id: orgData.id,
        user_id: user.id,
        role: 'owner'
      }])

    if (memberError) {
      console.error('Error creating membership:', memberError)
      // Try to clean up the organization if membership creation fails
      await supabaseAdmin
        .from('organizations')
        .delete()
        .eq('id', orgData.id)
      
      throw new Error('Failed to create organization membership')
    }

    return new Response(
      JSON.stringify({
        success: true,
        organization: orgData
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error in create-organization function:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
